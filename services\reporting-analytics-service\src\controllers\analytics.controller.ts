import { Request, Response } from 'express';
import { AnalyticsModel } from '../models/Analytics.model';
import { AuthenticatedRequest } from '../../../shared/middleware/auth';
import { ApiResponse } from '../../../shared/types';

export class AnalyticsController {

  async getDashboardAnalytics(req: Request, res: Response) {
    try {
      const period = req.query.period as string || 'daily';
      const days = parseInt(req.query.days as string) || 30;
      
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const analytics = await AnalyticsModel.find({
        type: period,
        date: { $gte: startDate, $lte: endDate }
      }).sort({ date: 1 });

      // Get latest analytics for overview
      const latestAnalytics = await AnalyticsModel.findOne({ type: period })
        .sort({ date: -1 });

      res.json({
        success: true,
        message: 'Dashboard analytics retrieved successfully',
        data: {
          overview: latestAnalytics,
          trends: analytics,
          period,
          dateRange: { startDate, endDate }
        }
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get dashboard analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve dashboard analytics',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getUserAnalytics(req: Request, res: Response) {
    try {
      const period = req.query.period as string || 'daily';
      const days = parseInt(req.query.days as string) || 30;
      
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const userAnalytics = await AnalyticsModel.find({
        type: period,
        date: { $gte: startDate, $lte: endDate }
      })
      .select('date totalUsers newUsers activeUsers studentCount tutorCount')
      .sort({ date: 1 });

      res.json({
        success: true,
        message: 'User analytics retrieved successfully',
        data: {
          analytics: userAnalytics,
          period,
          dateRange: { startDate, endDate }
        }
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get user analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve user analytics',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getCourseAnalytics(req: Request, res: Response) {
    try {
      const period = req.query.period as string || 'daily';
      const days = parseInt(req.query.days as string) || 30;
      
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const courseAnalytics = await AnalyticsModel.find({
        type: period,
        date: { $gte: startDate, $lte: endDate }
      })
      .select('date totalCourses publishedCourses newCourses totalEnrollments newEnrollments')
      .sort({ date: 1 });

      res.json({
        success: true,
        message: 'Course analytics retrieved successfully',
        data: {
          analytics: courseAnalytics,
          period,
          dateRange: { startDate, endDate }
        }
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get course analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve course analytics',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getRevenueAnalytics(req: Request, res: Response) {
    try {
      const period = req.query.period as string || 'daily';
      const days = parseInt(req.query.days as string) || 30;
      
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const revenueAnalytics = await AnalyticsModel.find({
        type: period,
        date: { $gte: startDate, $lte: endDate }
      })
      .select('date totalRevenue completedPayments failedPayments averageOrderValue')
      .sort({ date: 1 });

      // Calculate totals
      const totals = revenueAnalytics.reduce((acc, item) => ({
        totalRevenue: acc.totalRevenue + item.totalRevenue,
        completedPayments: acc.completedPayments + item.completedPayments,
        failedPayments: acc.failedPayments + item.failedPayments,
      }), { totalRevenue: 0, completedPayments: 0, failedPayments: 0 });

      res.json({
        success: true,
        message: 'Revenue analytics retrieved successfully',
        data: {
          analytics: revenueAnalytics,
          totals,
          period,
          dateRange: { startDate, endDate }
        }
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get revenue analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve revenue analytics',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getPerformanceAnalytics(req: Request, res: Response) {
    try {
      const period = req.query.period as string || 'daily';
      const days = parseInt(req.query.days as string) || 30;
      
      const endDate = new Date();
      const startDate = new Date();
      startDate.setDate(startDate.getDate() - days);

      const performanceAnalytics = await AnalyticsModel.find({
        type: period,
        date: { $gte: startDate, $lte: endDate }
      })
      .select('date totalAssignments completedAssignments failedAssignments averageScore completionRate failureRate')
      .sort({ date: 1 });

      res.json({
        success: true,
        message: 'Performance analytics retrieved successfully',
        data: {
          analytics: performanceAnalytics,
          period,
          dateRange: { startDate, endDate }
        }
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get performance analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve performance analytics',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getTutorCourseAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      // TODO: Query course service for tutor's course data
      // This is a placeholder implementation
      res.json({
        success: true,
        message: 'Tutor course analytics retrieved successfully',
        data: {
          message: 'This endpoint needs to query course service for tutor-specific data',
          tutorId: req.user!.uid
        }
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get tutor course analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve tutor course analytics',
        errors: [error.message]
      } as ApiResponse);
    }
  }

  async getTutorStudentAnalytics(req: AuthenticatedRequest, res: Response) {
    try {
      // TODO: Query subscription service for tutor's student data
      // This is a placeholder implementation
      res.json({
        success: true,
        message: 'Tutor student analytics retrieved successfully',
        data: {
          message: 'This endpoint needs to query subscription service for tutor-specific student data',
          tutorId: req.user!.uid
        }
      } as ApiResponse);

    } catch (error: any) {
      console.error('Get tutor student analytics error:', error);
      res.status(500).json({
        success: false,
        message: 'Failed to retrieve tutor student analytics',
        errors: [error.message]
      } as ApiResponse);
    }
  }
}