import express from 'express';
import { Reports<PERSON>ontroller } from '../controllers/reports.controller';
import { authenticateToken, requireRole } from '../../../shared/middleware/auth';

const router = express.Router();
const reportsController = new ReportsController();

// Admin routes for reports
router.get('/platform-overview', authenticateToken, requireRole(['admin']), reportsController.getPlatformOverviewReport);
router.get('/course-performance', authenticateToken, requireRole(['admin']), reportsController.getCoursePerformanceReport);
router.get('/financial', authenticateToken, requireRole(['admin']), reportsController.getFinancialReport);
router.get('/user-engagement', authenticateToken, requireRole(['admin']), reportsController.getUserEngagementReport);

// Tutor reports
router.get('/tutor/performance', authenticateToken, requireRole(['tutor']), reportsController.getTutorPerformanceReport);

export default router;